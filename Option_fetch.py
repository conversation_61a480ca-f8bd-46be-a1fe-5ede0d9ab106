import os
import requests
import pandas as pd
import datetime
import pytz
from Dhan_Tradehull import Tradehull
from dhanhq import dhanhq
import time


def get_instrument_file():
		global instrument_df
		current_date = time.strftime("%Y-%m-%d")
		expected_file = 'all_instrument ' + str(current_date) + '.csv'
		for item in os.listdir("Dependencies"):
			path = os.path.join(item)

			if (item.startswith('all_instrument')) and (current_date not in item.split(" ")[1]):
				if os.path.isfile("Dependencies\\" + path):
					os.remove("Dependencies\\" + path)

		if expected_file in os.listdir("Dependencies"):
			try:
				print(f"reading existing file {expected_file}")
				instrument_df = pd.read_csv("Dependencies\\" + expected_file, low_memory=False)
			except Exception as e:
				print(
					"This BOT Is Instrument file is not generated completely, Picking New File from Dhan Again")
				instrument_df = pd.read_csv("https://images.dhan.co/api-data/api-scrip-master.csv", low_memory=False)

		else:
			# this will fetch instrument_df file from Dhan
			print("This BOT Is Picking New File From Dhan")
			instrument_df = pd.read_csv("https://images.dhan.co/api-data/api-scrip-master.csv", low_memory=False)

		return instrument_df

instrument_df = get_instrument_file()

# print(instrument_df.head(5))








url = "https://api.dhan.co/charts/intraday"

client_code = "1105577608"
token_id = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUwOTU2MDc2LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw"
tsl = Tradehull(client_code, token_id)

Dhan = dhanhq(client_code, token_id)
print("self.Dhan.FNO =", Dhan.FNO)

user_df = pd.read_csv('User_options_input.csv')

interval = '5'
exchange = 'NFO'

# for  loop
tradingsymbol = user_df.iloc[1]['DISPLAY_NAME']

# oh
#  Dhan.intraday_minute_data('62376',Dhan.FNO, 'OPTIDX', '2025-05-16', '2025-06-20',int(interval))



script_exchange = {"NSE":Dhan.NSE, "NFO":Dhan.FNO, "BFO":"BSE_FNO", "CUR": Dhan.CUR, "BSE":Dhan.BSE, "MCX":Dhan.MCX, "INDEX":Dhan.INDEX}
instrument_exchange = {'NSE':"NSE",'BSE':"BSE",'NFO':'NSE','BFO':'BSE','MCX':'MCX','CUR':'NSE'}
exchange_segment = script_exchange[exchange]
index_exchange = {"NIFTY":'NSE',"BANKNIFTY":"NSE","FINNIFTY":"NSE","MIDCPNIFTY":"NSE","BANKEX":"BSE","SENSEX":"BSE"}
if tradingsymbol in index_exchange:
    exchange =index_exchange[tradingsymbol]

security_id = instrument_df[((instrument_df['SEM_TRADING_SYMBOL']==tradingsymbol)|(instrument_df['SEM_CUSTOM_SYMBOL']==tradingsymbol))&(instrument_df['SEM_EXM_EXCH_ID']==instrument_exchange[exchange])].iloc[-1]['SEM_SMST_SECURITY_ID']
instrument_type = instrument_df[((instrument_df['SEM_TRADING_SYMBOL']==tradingsymbol)|(instrument_df['SEM_CUSTOM_SYMBOL']==tradingsymbol))&(instrument_df['SEM_EXM_EXCH_ID']==instrument_exchange[exchange])].iloc[-1]['SEM_INSTRUMENT_NAME']

print("security_id", security_id)
print("instrument_type", instrument_type)
print('tradingsymbol', tradingsymbol)

test_ohlc = Dhan.intraday_minute_data(str(security_id),Dhan.FNO, instrument_type, '2025-05-16', '2025-06-20',int(interval))
# print(test_ohlc)
# test_ohlc = Dhan.intraday_minute_data(a,Dhan.FNO, b, '2025-05-16', '2025-06-20',int(interval))
# if tradingsymbol in instrument_df['SEM_TRADING_SYMBOL'].values:
#     print("yes", tradingsymbol)
#     print("Security_ID:***", str(security_id))
# else:
#     print("no", tradingsymbol)

df = pd.DataFrame(test_ohlc['data'])
df['timestamp'] = df['timestamp'].apply(lambda x: Dhan.convert_to_date_time(x))
print(df.head(10))
print(df.tail(10))
# ohlc = Dhan.historical_daily_data(int(security_id), Dhan.FNO,   OPTIDX,  from_date,  to_date,  int(expiry_code))


# index_name = 'NIFTY'
 
# no_strikes_to_replay = 20
 
# expiry_date = "26 JUN"
 
# start_time ='2025-05-20 09:15:00+05:30'
 
 
# today_date = datetime.datetime.now().date()
 
# index_data = tsl.get_historical_data(tradingsymbol=index_name, exchange='INDEX', timeframe="1")

# ATM_time_data = index_data[index_data['timestamp']==start_time]

# if not ATM_time_data.empty:
#     ATM_close = ATM_time_data.iloc[-1]['close']

# step = tsl.index_step_dict[index_name]

# ATM_Strike = round(ATM_close/step)*step

# all_strikes = [ATM_Strike+(step*i) for i in range(1,no_strikes_to_replay+1)] + [ATM_Strike-(step*i) for i in range(1,no_strikes_to_replay+1)] + [ATM_Strike]

# call_and_put_Strikes = [f"{index_name} {expiry_date} {strike} CALL" for strike in all_strikes] + [f"{index_name} {expiry_date} {strike} PUT" for strike in all_strikes]


# file_path = f'data/{index_name}/{today_date}/'

# directory = os.path.dirname(file_path)

# if not os.path.exists(directory):
#     os.makedirs(directory)
# user_df = pd.read_csv('User_options_input.csv')

# # for strike in call_and_put_Strikes:
# for display_name in user_df['DISPLAY_NAME']:
#     option_df = tsl.get_historical_data(tradingsymbol=display_name, exchange='NFO', timeframe="60")

#     print(display_name)
#     print(option_df.head(5))
#     print(option_df.tail(5))

