# historical_fetcher_v2.py
# Fetch past 5 trading days of intraday data using DhanHQ 2.0 compatible API (with from_date/to_date)

import os
import datetime
import pandas as pd
# from Dhan_Tradehull import Tradehull  # or replace with correct DhanHQ SDK import
# from dhanhq import dhanhq  # Ensure you have the correct DhanHQ SDK installed

# === Configuration ===
# client_code = "1105577608"
# token_id = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUwOTU2MDc2LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw"  # Replace with valid token

import requests
import datetime
import pandas as pd
import time
from dhanhq import dhanhq

from processing_functions import process_dataframe

token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUwOTU2MDc2LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw"

output_dir = "processed_files"

def fetch_dhan_data(interval, from_date, to_date):
    """
    Fetches historical data from Dhan API for a given instrument and date range.
    """
    security_id = "13"  # NIFTY
    exchange_segment = "IDX_I"
    instrument_type = "INDEX"
    
    print(f"  Fetching data for interval '{interval}' from {from_date} to {to_date}")

    # === Request Setup ===
    headers = {
        "access-token": token,
        "Content-Type": "application/json"
    }

    if interval == "daily":
        # Initialize DhanHQ client for daily data
        client = dhanhq("1105577608", token)
        res = client.historical_daily_data(
            security_id=security_id,
            exchange_segment=exchange_segment,
            instrument_type=instrument_type,
            expiry_code=0,
            from_date=from_date,
            to_date=to_date
        )
        data = res
    else:
        # Setup for intraday data
        payload = {
            "securityId": security_id,
            "exchangeSegment": exchange_segment,
            "instrument": instrument_type,
            "interval": interval,
            "oi": False,
            "fromDate": from_date,
            "toDate": to_date
        }
        url = "https://api.dhan.co/v2/charts/intraday"
        resp = requests.post(url, json=payload, headers=headers)
        data = resp.json()

    # Print the response to understand the structure
    print("API Response:")
    # print(data)
    print("\nResponse keys:", list(data.keys()) if isinstance(data, dict) else "Not a dictionary")

    # Check if the response is successful and has the expected structure
    if isinstance(data, dict):
        if "data" in data and interval != "daily":
            chart_data = data["data"]
            print("\nChart data keys:", list(chart_data.keys()) if isinstance(chart_data, dict) else "Chart data is not a dictionary")
            
            # Create DataFrame from individual lists
            df = pd.DataFrame({
                "open": chart_data["open"],
                "high": chart_data["high"],
                "low": chart_data["low"],
                "close": chart_data["close"],
                "volume": chart_data["volume"],
                "timestamp": pd.to_datetime(chart_data["timestamp"], unit="s", utc=True)
            })
        elif "data" in data and interval == "daily":
            # Handle daily data format
            df = pd.DataFrame(data["data"])
            df["timestamp"] = pd.to_datetime(df["timestamp"], unit="s", utc=True)
        elif all(key in data for key in ["open", "high", "low", "close", "volume", "timestamp"]):
            # Data is directly in the response
            df = pd.DataFrame({
                "open": data["open"],
                "high": data["high"],
                "low": data["low"],
                "close": data["close"],
                "volume": data["volume"],
                "timestamp": pd.to_datetime(data["timestamp"], unit="s", utc=True)
            })
        else:
            print("❌ Unexpected response structure. Cannot create DataFrame.")
            return None

    if df.empty:
        # This can happen if there's no new data in the requested range
        # print("  ! Fetched data is empty.")
        return None

    # Convert timestamp to IST (UTC+5:30)
    IST = datetime.timezone(datetime.timedelta(hours=5, minutes=30))
    # Convert to IST and overwrite 'timestamp'
    df["timestamp"] = df["timestamp"].dt.tz_convert(IST)

    # Filter out data points after 15:29 for intraday data
    if interval != "daily":
        df = df[df["timestamp"].dt.time <= datetime.time(15, 29)]

    # Reorder columns: IST timestamp first
    df = df[["timestamp", "open", "high", "low", "close", "volume"]]

    # Output preview
    # print("✅ Data fetched successfully.")
    
    return df


# Example usage
if __name__ == "__main__":
    fetch_interval_seconds = 50 # Define the interval in seconds
    market_close = datetime.time(15, 29)
    market_start = datetime.time(9, 15)
    
    # Create output directory once
    os.makedirs(output_dir, exist_ok=True)

    # --- State Management ---
    data_store = {}  # In-memory store for dataframes: {interval: DataFrame}
    first_run = True
    last_15_min_fetch_minute = -1

    while True:
        now = datetime.datetime.now()
        current_time = now.time()

        # Exit if market is closed or not yet open
        # The following check is commented out to allow the script to run for testing outside of market hours.
        # if not (market_start <= current_time <= market_close):
        #     print("Market is closed. Exiting...")
        #     break

        # --- Determine which intervals to fetch in this cycle ---
        intervals_to_fetch = set()
        if first_run:
            print("--- First run: scheduling all intervals for full historical fetch. ---")
            intervals_to_fetch.update(["1", "5", "60", "daily"])
            first_run = False
        else:
            intervals_to_fetch.update(["1", "5"])

        if now.minute % 15 == 0 and now.minute != last_15_min_fetch_minute:
            print(f"--- Quarter-hour mark ({now.strftime('%H:%M')}): Adding 60min fetch. ---")
            intervals_to_fetch.add("60")
            last_15_min_fetch_minute = now.minute

        # --- Fetch, Append, Process, and Save Data ---
        for interval in sorted(list(intervals_to_fetch)):
            print(f"\n--- Processing interval: {interval} ---")
            try:
                # 1. Determine Date Range for Fetch
                to_date_str = now.strftime("%Y-%m-%d %H:%M:%S")

                if interval not in data_store:
                    # Initial full fetch
                    print(f"  Initial fetch for interval '{interval}'. Calculating full historical range.")
                    end_date = now.date()
                    if interval == "1": from_date_obj = end_date - datetime.timedelta(days=10)
                    elif interval == "5": from_date_obj = end_date - datetime.timedelta(days=20)
                    # Note: Dhan API supports up to 60 days for 60min interval. Using 31 to match previous behavior.
                    elif interval == "60": from_date_obj = end_date - datetime.timedelta(days=31)
                    elif interval == "daily": from_date_obj = end_date - datetime.timedelta(days=365)
                    from_date_str = from_date_obj.strftime("%Y-%m-%d" if interval == "daily" else "%Y-%m-%d 09:15:00")
                else:
                    # Incremental fetch
                    last_timestamp = data_store[interval]['timestamp'].iloc[-1]
                    from_date_str = (last_timestamp + datetime.timedelta(minutes=1)).strftime("%Y-%m-%d %H:%M:%S")

                # 2. Fetch New Data
                new_data_df = fetch_dhan_data(interval, from_date_str, to_date_str)

                # 3. Update Data Store
                if new_data_df is not None and not new_data_df.empty:
                    print(f"  Fetched {len(new_data_df)} new records.")
                    if interval in data_store:
                        data_store[interval] = pd.concat([data_store[interval], new_data_df]).drop_duplicates(subset=['timestamp'], keep='last').sort_values(by='timestamp').reset_index(drop=True)
                    else:
                        data_store[interval] = new_data_df.sort_values(by='timestamp').reset_index(drop=True)
                    print(f"  Data store for '{interval}' now has {len(data_store[interval])} total records.")

                # 4. Process and Save
                if interval in data_store and not data_store[interval].empty:
                    df_processed = process_dataframe(data_store[interval].copy())
                    if df_processed is not None:
                        today_str = datetime.date.today().strftime("%Y-%m-%d")
                        interval_suffix = "daily" if interval == "daily" else f"{interval}min"
                        output_filename = f"NIFTY_INDEX_{interval_suffix}_{today_str}_processed.csv"
                        output_path = os.path.join(output_dir, output_filename)
                        df_processed.to_csv(output_path, index=False)
                        print(f"  ✓ Saved processed data to {output_path}")

            except Exception as e:
                print(f"  ✗ An unexpected error occurred for interval {interval}: {e}")

        print(f"\n--- Loop complete. Waiting for {fetch_interval_seconds} seconds. ---")
        time.sleep(fetch_interval_seconds) # Wait for the specified interval
